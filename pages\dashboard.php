<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/report_query_builder_semantic.php'; // Use the new semantic builder

// --- Configuration & Setup ---
$pdo = db_connect();
require_page_access('dashboard', 'view');
include '../includes/header.php';

// --- Operational KPIs Data Fetching ---

// 1. Open Breakdowns
$openBreakdownsStmt = $pdo->query("SELECT COUNT(*) FROM breakdown_reports WHERE status = 'گزارش شده'");
$openBreakdownsCount = $openBreakdownsStmt->fetchColumn();
$openBreakdownsListStmt = $pdo->query("
    SELECT br.id, br.problem_description, d.name as device_name 
    FROM breakdown_reports br
    JOIN devices d ON br.device_id = d.id
    WHERE br.status = 'گزارش شده' 
    ORDER BY br.report_datetime DESC
");
$openBreakdownsList = $openBreakdownsListStmt->fetchAll(PDO::FETCH_ASSOC);

// 2. Overdue Work Orders (Updated Logic)
$overdueWoStmt = $pdo->query("SELECT COUNT(*) FROM work_orders WHERE due_date < CURDATE() AND status = 'دستورکار صادر شد'");
$overdueWoCount = $overdueWoStmt->fetchColumn();
$overdueWoListStmt = $pdo->query("
    SELECT wo.id, wo.title, wo.due_date
    FROM work_orders wo
    WHERE wo.due_date < CURDATE() AND status = 'دستورکار صادر شد'
    ORDER BY wo.due_date ASC
");
$overdueWoList = $overdueWoListStmt->fetchAll(PDO::FETCH_ASSOC);


// 3. Overdue PMs
$overduePmStmt = $pdo->query("SELECT COUNT(*) FROM activities WHERE next_service_date < CURDATE()");
$overduePmCount = $overduePmStmt->fetchColumn();
$overduePmListStmt = $pdo->query("
    SELECT a.id, a.activity_name, d.name as device_name, a.next_service_date
    FROM activities a
    JOIN devices d ON a.device_id = d.id
    WHERE a.next_service_date < CURDATE() 
    ORDER BY a.next_service_date ASC
");
$overduePmList = $overduePmListStmt->fetchAll(PDO::FETCH_ASSOC);

// 4. Outsourced Devices
$outsourcedStmt = $pdo->query("SELECT COUNT(DISTINCT wo.device_id) FROM work_order_execution wex JOIN work_orders wo ON wex.work_order_id = wo.id WHERE wex.exit_date IS NOT NULL AND wex.back_date IS NULL");
$outsourcedCount = $outsourcedStmt->fetchColumn();
$outsourcedListStmt = $pdo->query("
    SELECT d.id, d.name as device_name, wex.exit_date
    FROM work_order_execution wex
    JOIN work_orders wo ON wex.work_order_id = wo.id
    JOIN devices d ON wo.device_id = d.id
    WHERE wex.exit_date IS NOT NULL AND wex.back_date IS NULL
    ORDER BY wex.exit_date DESC
");
$outsourcedList = $outsourcedListStmt->fetchAll(PDO::FETCH_ASSOC);


// --- User-configured Widgets Data Fetching ---
$widgetsStmt = $pdo->prepare("
    SELECT 
        w.id as widget_id, 
        w.display_options, 
        r.name as report_name, 
        r.description as report_description,
        r.definition_json
    FROM dashboard_report_widgets w 
    JOIN report_definitions r ON r.id = w.report_id 
    WHERE w.user_id = ? 
    ORDER BY w.position ASC, w.id ASC
");
$widgetsStmt->execute([current_user_id()]);
$widgets = $widgetsStmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>داشبورد مدیریتی</title>
    <link rel="stylesheet" href="../assets/css/all.min.css">
	<script src="../assets/js/chart.min.js"></script>
	<script src="../assets/js/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
       
        
        /* --- General Styles --- */
        body { 
            font-family: 'Vazirmatn', sans-serif; 
            background-color: #f0f2f5;
            color: #333;
            margin: 0;
        }
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        /* --- Header --- */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .dashboard-header h1 {
            font-size: 1.8rem;
            font-weight: bold;
            color: #111;
            margin: 0;
        }
        .dashboard-header p {
            margin-top: 0.25rem;
            font-size: 0.9rem;
            color: #555;
        }
        .btn-customize {
            background-color: #4f46e5;
            color: white;
            padding: 0.6rem 1rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        .btn-customize:hover {
            background-color: #4338ca;
        }
        .btn-customize i {
            margin-left: 0.5rem;
        }

        /* --- Section Titles --- */
        .section-title {
            font-size: 1.25rem;
            font-weight: bold;
            color: #374151;
            margin-bottom: 1.5rem;
        }

        /* --- Grid Layout --- */
        .grid {
            display: grid;
            gap: 1.5rem;
        }
        .grid-kpi {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
        
        /* --- KPI Card --- */
        .kpi-card {
            background-color: white;
            padding: 1.25rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0,0,0,0.08);
        }
        .kpi-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .kpi-card-header .value {
            font-size: 2rem;
            font-weight: bold;
			display:inline-flex;
			margin: auto;
        }
        .kpi-card-header .title {
            font-size: 1rem;
            font-weight: 600;
            margin-top: 0.25rem;
			display:inline-flex;
        }
        .kpi-card-header .subtitle {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
        }
        .kpi-icon {
            padding: 1rem;
            border-radius: 50%;
            font-size: 1.25rem;
			display:inline-flex;
			
        }
        .kpi-icon.red { background-color: #fee2e2; color: #dc2626; }
        .kpi-icon.yellow { background-color: #fef3c7; color: #d97706; }
        .kpi-icon.blue { background-color: #dbeafe; color: #2563eb; }
        .kpi-icon.green { background-color: #d1fae5; color: #059669; }
        
        .kpi-list-container {
            margin-top: 1rem;
            padding-top: 0.75rem;
            border-top: 1px solid #f3f4f6;
            flex-grow: 1;
            max-height: 130px;
            overflow-y: auto;
            transition: max-height 0.5s ease-in-out;
        }
        .kpi-list-container.is-expanded {
            max-height: 300px;
        }
        .kpi-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .kpi-list li a {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            text-decoration: none;
            color: #333;
        }
        .kpi-list li a:hover {
            background-color: #f9fafb;
        }
        .kpi-list .item-note {
            font-size: 0.75rem;
            color: #999;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .kpi-card-footer {
            margin-top: auto;
            padding-top: 0.5rem;
            text-align: center;
        }
        .view-all-btn {
            font-size: 0.85rem;
            font-weight: 600;
            color: #4f46e5;
            background: none;
            border: none;
            cursor: pointer;
        }
        .view-all-btn:hover {
            color: #3730a3;
        }

        /* --- Widget Grid --- */
        #dashboard-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(1, 1fr);
        }
        @media (min-width: 768px) {
            #dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (min-width: 1024px) {
            #dashboard-grid {
                grid-template-columns: repeat(4, 1fr);
            }
            .widget-card.col-span-1 { grid-column: span 1 / span 1; }
            .widget-card.col-span-2 { grid-column: span 2 / span 2; }
            .widget-card.col-span-4 { grid-column: span 4 / span 4; }
        }

        /* --- Widget Card --- */
        .widget-card {
            background-color: white;
            padding: 1rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            min-height: 350px;
        }
        .widget-card h4 {
            font-weight: bold;
            color: #1f2937;
            font-size: 1rem;
            margin: 0 0 0.25rem 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .widget-card p {
            font-size: 0.8rem;
            color: #6b7280;
            margin: 0 0 1rem 0;
        }
        .chart-container {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .empty-state {
            background-color: white;
            text-align: center;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        .empty-state i {
            font-size: 2.5rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }
        .empty-state h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        .empty-state p {
            color: #6b7280;
            margin-top: 0.5rem;
        }

        /* --- Spinner --- */
        @keyframes spin { to { transform: rotate(360deg); } }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #4f46e5;
            animation: spin 1s ease infinite;
        }
        .loading-state {
            text-align: center;
        }
        .loading-state p {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

    </style>
</head>
<body>

    <div class="container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div>
                <h1>داشبورد مدیریتی</h1>
                <p>نمای کلی از شاخص‌های کلیدی و عملکرد سیستم</p>
            </div>
            <a href="dashboard_reports.php" class="btn-customize">
                <i class="fas fa-edit"></i> سفارشی‌سازی گزارش‌ها
            </a>
        </div>

        <!-- NEW: Operational KPIs Section -->
        <div style="margin-bottom: 2rem;">
           
            <div class="grid grid-kpi">
                
                <!-- KPI Card: Open Breakdowns -->
                <div class="kpi-card">
                    <div class="kpi-card-header">
                        <div>
                            <p class="value" style="color: #dc2626;"><?php echo to_persian_digits($openBreakdownsCount); ?></p>
                            <p class="title">خرابی باز</p>
                            <p class="subtitle">گزارش‌هایی که هنوز به دستورکار تبدیل نشده‌اند.</p>
                        </div>
                        <div class="kpi-icon red"><i class="fas fa-tools"></i></div>
                    </div>
                    <div class="kpi-list-container">
                        <ul class="kpi-list">
                            <?php foreach(array_slice($openBreakdownsList, 0, 5) as $item): ?>
                            <li><a href="reports_list.php#report-<?php echo $item['id']; ?>"><span><?php echo htmlspecialchars($item['device_name']); ?></span> <span class="item-note"><?php echo htmlspecialchars($item['problem_description']); ?></span></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php if (count($openBreakdownsList) > 5): ?>
                    <div class="kpi-card-footer"><button class="view-all-btn">مشاهده همه</button></div>
                    <?php endif; ?>
                </div>

                <!-- KPI Card: Overdue Work Orders -->
                <div class="kpi-card">
                    <div class="kpi-card-header">
                        <div>
                            <p class="value" style="color: #d97706;"><?php echo to_persian_digits($overdueWoCount); ?></p>
                            <p class="title">دستورکار معوق</p>
                            <p class="subtitle">دستورکارهایی که موعدشان گذشته ولی هنوز شروع نشده‌اند.</p>
                        </div>
                        <div class="kpi-icon yellow"><i class="fas fa-calendar-times"></i></div>
                    </div>
                    <div class="kpi-list-container">
                        <ul class="kpi-list">
                            <?php foreach(array_slice($overdueWoList, 0, 5) as $item): ?>
                            <li><a href="work_order.php?id=<?php echo $item['id']; ?>"><span><?php echo htmlspecialchars($item['title']); ?></span><span class="item-note"><?php echo htmlspecialchars($item['due_date']); ?></span></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php if (count($overdueWoList) > 5): ?>
                    <div class="kpi-card-footer"><button class="view-all-btn">مشاهده همه</button></div>
                    <?php endif; ?>
                </div>

                <!-- KPI Card: Overdue PMs -->
                <div class="kpi-card">
                    <div class="kpi-card-header">
                        <div>
                            <p class="value" style="color: #2563eb;"><?php echo to_persian_digits($overduePmCount); ?></p>
                            <p class="title">نت پیشگیرانه معوق</p>
                            <p class="subtitle">فعالیت‌های PM که موعدشان فرا رسیده است.</p>
                        </div>
                        <div class="kpi-icon blue"><i class="fas fa-tasks"></i></div>
                    </div>
                    <div class="kpi-list-container">
                       <ul class="kpi-list">
                           <?php foreach(array_slice($overduePmList, 0, 5) as $item): ?>
                            <li><a href="activities.php?id=<?php echo $item['id']; ?>"><span><?php echo htmlspecialchars($item['activity_name']); ?></span><span class="item-note"><?php echo htmlspecialchars($item['device_name']); ?></span></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php if (count($overduePmList) > 5): ?>
                    <div class="kpi-card-footer"><button class="view-all-btn">مشاهده همه</button></div>
                    <?php endif; ?>
                </div>
                
                <!-- KPI Card: Outsourced Devices -->
                <div class="kpi-card">
                    <div class="kpi-card-header">
                        <div>
                            <p class="value" style="color: #059669;"><?php echo to_persian_digits($outsourcedCount); ?></p>
                            <p class="title">کار برون‌سپاری شده</p>
                            <p class="subtitle">دستگاه‌هایی که برای تعمیر خارج شده‌اند.</p>
                        </div>
                        <div class="kpi-icon green"><i class="fas fa-shipping-fast"></i></div>
                    </div>
                    <div class="kpi-list-container">
                        <ul class="kpi-list">
                            <?php foreach(array_slice($outsourcedList, 0, 5) as $item): ?>
                            <li><a href="devices.php?id=<?php echo $item['id']; ?>"><span><?php echo htmlspecialchars($item['device_name']); ?></span><span class="item-note"><?php echo htmlspecialchars($item['exit_date']); ?></span></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php if (count($outsourcedList) > 5): ?>
                    <div class="kpi-card-footer"><button class="view-all-btn">مشاهده همه</button></div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- User-configured Analytical Widgets -->
        <?php if (empty($widgets)): ?>
            <div class="empty-state">
                <i class="fas fa-chart-pie"></i>
                <h3>گزارش تحلیلی انتخاب نشده است!</h3>
                <p>برای افزودن گزارش‌های نموداری و جدولی به این بخش، از دکمه سفارشی‌سازی استفاده کنید.</p>
            </div>
        <?php else: ?>
            <div id="dashboard-grid">
                <?php foreach ($widgets as $widget): 
                    $options = json_decode($widget['display_options'], true);
                    $definition = json_decode($widget['definition_json'], true);
                    $chartType = $options['type'] ?? $definition['chartType'] ?? 'table';
                    
                    $colSpan = 'col-span-2'; // Default
                    if ($chartType === 'kpi') $colSpan = 'col-span-1';
                    if ($chartType === 'pie' || $chartType === 'doughnut') $colSpan = 'col-span-2';
                    if ($chartType === 'table') $colSpan = 'col-span-4';
                ?>
                    <div class="widget-card <?php echo $colSpan; ?>" 
                         data-widget-id="<?php echo $widget['widget_id']; ?>"
                         data-definition='<?php echo htmlspecialchars($widget['definition_json'], ENT_QUOTES, 'UTF-8'); ?>'
                         data-chart-type='<?php echo htmlspecialchars($chartType, ENT_QUOTES, 'UTF-8'); ?>'>
                        
                        <h4 title="<?php echo htmlspecialchars($widget['report_name']); ?>">
                            <?php echo htmlspecialchars($widget['report_name']); ?>
                        </h4>
                        <p><?php echo htmlspecialchars($widget['report_description']); ?></p>
                        
                        <div class="chart-container">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>در حال بارگذاری داده‌ها...</p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // --- Analytical Widgets Logic ---
    const widgets = document.querySelectorAll('.widget-card');
    widgets.forEach(loadWidgetData);

    // --- KPI Cards "View All" Logic ---
    document.querySelectorAll('.view-all-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            const card = e.target.closest('.kpi-card');
            const listContainer = card.querySelector('.kpi-list-container');
            const isExpanded = listContainer.classList.contains('is-expanded');

            if (isExpanded) {
                listContainer.classList.remove('is-expanded');
                e.target.textContent = 'مشاهده همه';
            } else {
                listContainer.classList.add('is-expanded');
                e.target.textContent = 'نمایش کمتر';
            }
        });
    });

    // --- Main Functions ---
    async function loadWidgetData(widget) {
        const definition = JSON.parse(widget.dataset.definition);
        const chartType = widget.dataset.chartType; 
        const container = widget.querySelector('.chart-container');
        
        try {
            const formData = new FormData();
            formData.append('action', 'get_report_data');
            formData.append('definition', JSON.stringify(definition));

            const response = await fetch('dashboard_reports.php', { method: 'POST', body: formData });
            if (!response.ok) throw new Error(`Server responded with status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderWidget(container, result, chartType);
            } else {
                throw new Error(result.message || 'Failed to load report data.');
            }
        } catch (error) {
            container.innerHTML = `<div style="text-align: center; color: #dc2626; padding: 1rem;"><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 0.5rem;"></i><p style="font-weight: 600;">خطا در بارگذاری</p><p style="font-size: 0.8rem; margin-top: 0.25rem;">${error.message}</p></div>`;
        }
    }

    function renderWidget(container, response, chartType) {
        const { data } = response;
        const { columns, rows, warning } = data;
        container.innerHTML = ''; 

        if (warning) {
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = 'padding: 0.5rem; margin-bottom: 0.5rem; font-size: 0.8rem; color: #92400e; background-color: #fef3c7; border-radius: 8px;';
            warningDiv.innerText = warning;
            container.appendChild(warningDiv);
        }

        if (!rows || rows.length === 0) {
            container.innerHTML += '<div style="text-align: center; color: #6b7280; padding: 1rem;"><i class="fas fa-info-circle" style="margin-right: 0.5rem;"></i>داده‌ای برای نمایش وجود ندارد.</div>';
            return;
        }

        switch(chartType) {
            case 'kpi': renderKpi(container, columns, rows); break;
            case 'table': renderTable(container, columns, rows); break;
            default: renderChart(container, columns, rows, chartType);
        }
    }

    function renderKpi(container, columns, rows) {
        let kpiHtml = '<div style="width: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center;">';
        const value = rows[0][columns[0].key] ?? 'N/A';
        const formattedValue = !isNaN(parseFloat(value)) ? parseFloat(value).toLocaleString('fa-IR') : value;
        
        kpiHtml += `<div style="font-size: 2.8rem; font-weight: bold; color: #4f46e5;">${formattedValue}</div>`;
        kpiHtml += `<h5 style="font-size: 0.9rem; color: #6b7280; margin-top: 0.5rem;">${columns[0].label}</h5>`;
        kpiHtml += '</div>';
        container.innerHTML = kpiHtml;
    }

    function renderTable(container, columns, rows) {
        let tableHtml = '<div style="width: 100%; overflow-x: auto;"><table style="width: 100%; font-size: 0.85rem; text-align: right; border-collapse: collapse;"><thead><tr style="background-color: #f9fafb;">';
        columns.forEach(col => tableHtml += `<th style="padding: 0.75rem 1rem; color: #374151; font-weight: 600;">${col.label}</th>`);
        tableHtml += '</tr></thead><tbody>';
        rows.forEach(row => {
            tableHtml += '<tr style="border-bottom: 1px solid #f3f4f6;">';
            columns.forEach(col => tableHtml += `<td style="padding: 0.75rem 1rem;">${row[col.key] ?? ''}</td>`);
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table></div>';
        container.innerHTML = tableHtml;
    }

    function renderChart(container, columns, rows, chartType) {
        const canvas = document.createElement('canvas');
        container.appendChild(canvas);
        const ctx = canvas.getContext('2d');

        const labels = rows.map(row => row[columns[0].key]);
        const datasets = [];
        const bgColors = ['rgba(79, 70, 229, 0.7)', 'rgba(52, 211, 153, 0.7)', 'rgba(245, 158, 11, 0.7)', 'rgba(239, 68, 68, 0.7)', 'rgba(99, 102, 241, 0.7)', 'rgba(139, 92, 246, 0.7)'];
        const borderColors = ['#4f46e5', '#34d399', '#f59e0b', '#ef4444', '#6366f1', '#8b5cf6'];

        for (let i = 1; i < columns.length; i++) {
            datasets.push({
                label: columns[i].label,
                data: rows.map(row => row[columns[i].key]),
                backgroundColor: chartType === 'pie' || chartType === 'doughnut' ? bgColors : bgColors[(i - 1) % bgColors.length],
                borderColor: borderColors[(i - 1) % borderColors.length],
                borderWidth: chartType === 'line' ? 2 : 1,
                fill: chartType === 'line',
                tension: 0.1
            });
        }

        new Chart(ctx, {
            type: chartType,
            data: { labels, datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'index', intersect: false },
                scales: (chartType === 'bar' || chartType === 'line') ? {
                    y: { beginAtZero: true, grid: { color: '#e5e7eb' } },
                    x: { grid: { display: false } }
                } : {},
                plugins: {
                    legend: { position: 'bottom', labels: { font: { family: 'Vazirmatn' } } },
                    tooltip: { rtl: true, bodyFont: { family: 'Vazirmatn' }, titleFont: { family: 'Vazirmatn' } }
                }
            }
        });
    }
});
</script>

</body>
</html>

<?php include '../includes/footer.php'; ?>
