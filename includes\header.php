<!-- مودال یادآوری‌های دریافتی -->

<style>
/* MOBILE header dropdown (unchanged) */
.reminders-dropdown {
    position: absolute;
    top: 48px;
    left: 0;
    min-width: 320px;
    max-width: 400px;
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(255,102,0,0.15);
    border: none;
    z-index: 99999;
    padding: 1rem 1rem 0.5rem 1rem;
    display: none;
}
.reminders-dropdown.open { display: block; }
.reminders-btn-counter {
    background: #dc3545;
    color: #fff;
    font-size: 0.85rem;
    font-weight: bold;
    border-radius: 50%;
    padding: 2px 7px;
    margin-right: 4px;
    margin-left: 2px;
    vertical-align: top;
    display: inline-block;
}
.reminders-dropdown-header {
    color: #d94307;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}
.reminders-dropdown-list {
    max-height: 300px;
    overflow-y: auto;
    padding-bottom: 8px;
}
.reminder-item {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(255,102,0,0.10);
    margin-bottom: 12px;
    padding: 12px 10px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}
.reminder-item .reminder-title { font-size: 1rem; font-weight: 700; color: #d94307; }
.reminder-item .reminder-date { font-size: 0.9rem; color: #888; }
.reminder-item .reminder-message { font-size: 0.95rem; color: #333; margin: 4px 0; white-space: pre-line; }
.reminder-item .reminder-sender { font-size: 0.9rem; color: #a16207; display: flex; align-items: center; gap: 4px; }
.reminder-item .reminder-report-link { font-size: 0.9rem; color: #2563eb; text-decoration: underline; margin-top: 2px; }
@media (max-width: 600px) {
    .reminders-dropdown { min-width: 95vw; left: 2vw; padding: 0.5rem; }
}

/* DESKTOP floating action button (FAB) + panel */
.reminders-fab {
    position: fixed;
    left: 24px;
    bottom: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg,#ffb366,#d94307);
    color: #fff;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 24px rgba(217,67,7,0.28);
    cursor: pointer;
    z-index: 99995;
}
.reminders-fab i { font-size: 22px; }
.reminders-fab-badge {
    position: absolute;
    top: -6px; right: -6px;
    background: #dc3545; color: #fff;
    border-radius: 999px; font-size: 12px; line-height: 1;
    padding: 2px 6px; display: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}
.reminders-panel {
    position: fixed;
    left: 24px; bottom: 90px;
    width: 360px; max-width: calc(100vw - 40px);
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 12px 32px rgba(0,0,0,0.18);
    border: none;
    z-index: 99996;
    padding: 12px 12px 8px 12px;
    display: none;
}
.reminders-panel.open { display: block; }
.reminders-panel-header {
    display: flex; align-items: center; justify-content: space-between;
    color: #d94307; font-size: 1.05rem; font-weight: 700; margin-bottom: 8px;
}
.reminders-close-btn { background: none; border: none; color: #666; cursor: pointer; font-size: 18px; }
.reminders-panel-list { max-height: 320px; overflow-y: auto; }

/* Ensure FAB not visible on small screens (we only build desktop in JS, but keep safe) */
@media (max-width: 600px) {
    .reminders-fab, .reminders-panel { display: none !important; }
}
</style>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const isMobile = window.matchMedia('(max-width: 600px)').matches;

    // Helper: fetch and update unread counter in whichever UI exists
    function updateCounter() {
        fetch('../pages/ajax_get_reminders_count.php')
            .then(res => res.json())
            .then(data => {
                const count = (data && data.success) ? Number(data.count) : 0;
                const headerCounter = document.getElementById('reminders-counter');
                const fabBadge = document.getElementById('reminders-fab-badge');
                if (headerCounter) {
                    if (count > 0) {
                        headerCounter.textContent = count;
                        headerCounter.style.display = 'inline-block';
                    } else {
                        headerCounter.style.display = 'none';
                    }
                }
                if (fabBadge) {
                    if (count > 0) {
                        fabBadge.textContent = count;
                        fabBadge.style.display = 'inline-block';
                    } else {
                        fabBadge.style.display = 'none';
                    }
                }
            })
            .catch(() => {/* ignore */});
    }



    if (isMobile) {
        // MOBILE: keep button in header (as before)
        const remindersBtn = document.createElement('button');
        remindersBtn.id = 'reminders-btn';
        remindersBtn.className = 'btn';
        remindersBtn.style = 'background:linear-gradient(90deg,#ffb366,#d94307);color:#fff;border-radius:8px;padding:8px 16px;font-weight:600;box-shadow:0 2px 8px rgba(255,102,0,0.12);margin-left:10px;display:flex;align-items:center;gap:6px;position:relative;';
        remindersBtn.innerHTML = '<i class="fas fa-bell"></i> یادآوری‌های دریافتی <span id="reminders-counter" class="reminders-btn-counter" style="display:none;">0</span>';
        const header = document.querySelector('.main-header');
        const userSection = document.querySelector('.header-user');
        if (header && userSection) { header.insertBefore(remindersBtn, userSection); }

        // Dropdown box anchored to header
        const dropdown = document.createElement('div');
        dropdown.id = 'reminders-dropdown';
        dropdown.className = 'reminders-dropdown';
        dropdown.innerHTML = '<div class="reminders-dropdown-header">یادآوری‌های دریافتی من</div><div id="reminders-dropdown-list" class="reminders-dropdown-list"></div>';
        remindersBtn.parentNode.insertBefore(dropdown, remindersBtn.nextSibling);

        remindersBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.classList.toggle('open');
            if (dropdown.classList.contains('open')) {
                fetch('../pages/ajax_get_reminders.php')
                    .then(res => res.json())
                    .then(data => {
                        const list = document.getElementById('reminders-dropdown-list');
                        list.innerHTML = '';
                        if (!data.success || !data.reminders || data.reminders.length === 0) {
                            list.innerHTML = '<div style="text-align:center;color:#888;padding:16px 0;font-size:1rem;">هیچ یادآوری‌ای برای شما ثبت نشده است.</div>';
                        } else {
                            data.reminders.forEach(reminder => {
                                list.innerHTML += `
                                    <div class="reminder-item">
                                        <div class="reminder-title"><i class='fas fa-bell'></i> یادآوری جدید</div>
                                        <div class="reminder-date"><i class='fas fa-clock'></i> ${reminder.created_at_shamsi}</div>
                                        <div class="reminder-message">${reminder.message}</div>
                                        <div class="reminder-sender"><i class='fas fa-user'></i> ارسال‌کننده: ${reminder.sender_name}</div>
                                        <div class="reminder-report-link"><a href='../pages/reports_list.php?highlight=${reminder.report_id}' target='_blank'>مشاهده گزارش مرتبط</a></div>
                                    </div>
                                `;
                            });
                        }
                    });
            }
        });
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target) && e.target !== remindersBtn) {
                dropdown.classList.remove('open');
            }
        });

        updateCounter();
        setInterval(updateCounter, 60000);
    } else {
        // DESKTOP: Floating Action Button (FAB) and panel
        const fab = document.createElement('button');
        fab.id = 'reminders-fab';
        fab.className = 'reminders-fab';
        fab.setAttribute('aria-label', 'یادآوری‌ها');
        fab.innerHTML = '<i class="fas fa-bell"></i><span id="reminders-fab-badge" class="reminders-fab-badge">0</span>';
        document.body.appendChild(fab);

        const panel = document.createElement('div');
        panel.id = 'reminders-panel';
        panel.className = 'reminders-panel';
        panel.innerHTML = `
            <div class="reminders-panel-header">
                <span>یادآوری‌های دریافتی من</span>
                <button class="reminders-close-btn" aria-label="بستن"><i class="fas fa-times"></i></button>
            </div>
            <div id="reminders-panel-list" class="reminders-panel-list"></div>
        `;
        document.body.appendChild(panel);

        function loadRemindersIntoPanel() {
            const list = document.getElementById('reminders-panel-list');
            list.innerHTML = '<div style="text-align:center;color:#999;padding:12px">در حال بارگذاری...</div>';
            fetch('../pages/ajax_get_reminders.php')
                .then(res => res.json())
                .then(data => {
                    list.innerHTML = '';
                    if (!data.success || !data.reminders || data.reminders.length === 0) {
                        list.innerHTML = '<div style="text-align:center;color:#888;padding:16px 0;font-size:1rem;">هیچ یادآوری‌ای برای شما ثبت نشده است.</div>';
                    } else {
                        data.reminders.forEach(reminder => {
                            list.innerHTML += `
                                <div class="reminder-item">
                                    <div class="reminder-title"><i class='fas fa-bell'></i> یادآوری جدید</div>
                                    <div class="reminder-date"><i class='fas fa-clock'></i> ${reminder.created_at_shamsi}</div>
                                    <div class="reminder-message">${reminder.message}</div>
                                    <div class="reminder-sender"><i class='fas fa-user'></i> ارسال‌کننده: ${reminder.sender_name}</div>
                                    <div class="reminder-report-link"><a href='../pages/reports_list.php?highlight=${reminder.report_id}' target='_blank'>مشاهده گزارش مرتبط</a></div>
                                </div>
                            `;
                        });
                    }
                });
        }

        fab.addEventListener('click', function(e) {
            e.stopPropagation();
            const isOpen = panel.classList.contains('open');
            if (isOpen) {
                panel.classList.remove('open');
            } else {
                panel.classList.add('open');
                loadRemindersIntoPanel();
            }
        });
        panel.addEventListener('click', e => e.stopPropagation());
        document.addEventListener('click', function() { panel.classList.remove('open'); });
        panel.querySelector('.reminders-close-btn').addEventListener('click', function() {
            panel.classList.remove('open');
        });

        updateCounter();
        setInterval(updateCounter, 60000);
    }
});
</script>
<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نرم افزار نت دمباز</title>
	<link rel="stylesheet" href="../assets/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css" />
    <link rel="stylesheet" href="../assets/css/buttons.css" />
    <link rel="stylesheet" href="../assets/css/select2.min.css" />
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css" />
    <link rel="stylesheet" href="../assets/css/file_uploader.css" />
    <style>
        /* Toast Message Styles - Global */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 10px;
            border-right: 4px solid;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            opacity: 0;
        }
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-success {
            border-color: #28a745;
            color: #155724;
        }
        .toast-warning {
            border-color: #ffc107;
            color: #856404;
        }
        .toast-danger {
            border-color: #dc3545;
            color: #721c24;
        }
        .toast-info {
            border-color: #17a2b8;
            color: #0c5460;
        }
        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .toast-title {
            font-weight: 600;
            font-size: 0.9rem;
        }
        .toast-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            line-height: 1;
        }
        .toast-message {
            font-size: 0.85rem;
            line-height: 1.4;
        }
    </style>
</head>
<body>
<div id="toast-container" class="toast-container"></div>

<header class="main-header">
    <!-- Logo/Brand Section -->
    <div class="header-brand">
        <div class="brand-logo">
            <i class="fas fa-cogs"></i>
            <span class="brand-text">نت دمباز</span>
        </div>
    </div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle navigation">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
    </button>

    <!-- Navigation -->
    <nav class="main-navigation" id="main-navigation">
        <?php
        // بارگذاری سیستم مجوزها اگر هنوز بارگذاری نشده
        if (!isset($permission_system)) {
            require_once __DIR__ . '/PermissionSystem.php';
            require_once __DIR__ . '/../db_connection.php';
            $pdo = db_connect();
            $permission_system = new PermissionSystem($pdo);
        }

        // اطمینان از ثبت صفحات/مجوزهای سازنده گزارش و مدیریت داشبورد
        if (file_exists(__DIR__ . '/report_query_builder.php')) {
            require_once __DIR__ . '/report_query_builder.php';
            try { ensureReportInfrastructure($pdo); } catch (Throwable $e) { /* ignore */ }
        }

        // دریافت صفحات قابل دسترس برای کاربر جاری
        $accessible_pages = get_accessible_pages();

        // تقسیم صفحات به دسته‌های مختلف
        $admin_pages = ['dashboard_reports', 'report_builder', 'user_management'];
        $reports_pages = ['submit_report', 'reports_list', 'repair_approvals'];

        // ترتیب صفحات مطابق درخواست: داشبورد، دستورکار، مدیریت فعالیت‌ها، مدیریت دستگاه‌ها، مدیریت گزارش‌ها، تنظیمات مدیریتی، وظایف من
        $page_order = [
            'dashboard' => 1,
            'work_order' => 2,
            'activities' => 3,
            'devices' => 4,
            'my_tasks' => 7, // وظایف من در انتها
            'profile' => 8,
            'reminders' => 9
        ];

        $regular_pages = [];
        $admin_menu_pages = [];
        $reports_menu_pages = [];

        foreach ($accessible_pages as $page) {
            if (in_array($page['page_name'], $admin_pages)) {
                $admin_menu_pages[] = $page;
            } elseif (in_array($page['page_name'], $reports_pages)) {
                $reports_menu_pages[] = $page;
            } else {
                $regular_pages[] = $page;
            }
        }

        // مرتب کردن صفحات عادی بر اساس ترتیب تعریف شده
        usort($regular_pages, function($a, $b) use ($page_order) {
            $order_a = isset($page_order[$a['page_name']]) ? $page_order[$a['page_name']] : 999;
            $order_b = isset($page_order[$b['page_name']]) ? $page_order[$b['page_name']] : 999;
            return $order_a - $order_b;
        });
        ?>

        <ul class="nav-menu">
            <?php
            // تابع کمکی برای یافتن صفحه بر اساس نام
            function findPageByName($pages, $page_name) {
                foreach ($pages as $page) {
                    if ($page['page_name'] === $page_name) {
                        return $page;
                    }
                }
                return null;
            }

            // تابع کمکی برای نمایش آیتم منو
            function renderMenuItem($page, $pdo) {
                if (!$page) return;

                $page_url = "../pages/" . $page['file_path'];
                $icon = $page['icon'] ? '<i class="' . $page['icon'] . '"></i>' : '';

                // اضافه کردن شمارنده برای وظایف من
                $badge_html = '';
                if ($page['page_name'] === 'my_tasks') {
                    try {
                        $current_user_id = current_user_id();
                        if ($current_user_id) {
                            $tasks_count_stmt = $pdo->prepare("
                                SELECT COUNT(DISTINCT wo.id)
                                FROM work_orders wo
                                JOIN work_order_assignees woa ON wo.id = woa.work_order_id
                                WHERE woa.user_id = ? AND wo.status IN ('دستورکار صادر شد', 'در حال انجام', 'پایان تعمیر','برگشت جهت اصلاح')
                            ");
                            $tasks_count_stmt->execute([$current_user_id]);
                            $tasks_count = (int)$tasks_count_stmt->fetchColumn();

                            // Show badge only if there are tasks
                            if ($tasks_count > 0) {
                                $badge_html = '<span class="nav-badge">' . $tasks_count . '</span>';
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Error counting tasks: " . $e->getMessage());
                        // On error, do not display a badge to avoid confusion
                        $badge_html = '';
                    }
                }

                echo '<li class="nav-item">';
                echo '<a href="' . $page_url . '" class="nav-link">';
                echo $icon;
                echo '<span>' . htmlspecialchars($page['display_name']) . '</span>';
                echo $badge_html;
                echo '</a>';
                echo '</li>';
            }

            // ترتیب صحیح منو: داشبورد، دستورکار، مدیریت فعالیت‌ها، مدیریت دستگاه‌ها، مدیریت گزارش‌ها، تنظیمات مدیریتی، وظایف من
            $all_pages = array_merge($regular_pages, $admin_menu_pages, $reports_menu_pages);

            // Debug: show page counts
            // echo "<!-- Debug: Regular pages: " . count($regular_pages) . ", Admin pages: " . count($admin_menu_pages) . ", Reports pages: " . count($reports_menu_pages) . " -->";

            // 1. داشبورد
            renderMenuItem(findPageByName($all_pages, 'dashboard'), $pdo);

            // 2. دستور کار
            renderMenuItem(findPageByName($all_pages, 'work_order'), $pdo);

            // 3. مدیریت فعالیت‌ها
            renderMenuItem(findPageByName($all_pages, 'activities'), $pdo);

            // 4. مدیریت دستگاه‌ها
            renderMenuItem(findPageByName($all_pages, 'devices'), $pdo);
            ?>

            <!-- 5. مدیریت گزارش‌ها -->
            <?php if (!empty($reports_menu_pages)): ?>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>مدیریت گزارش‌ها</span>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <?php foreach ($reports_menu_pages as $page):
                            $page_url = "../pages/" . $page['file_path'];
                            $icon = $page['icon'] ? '<i class="' . $page['icon'] . '"></i>' : '';
                        ?>
                            <li>
                                <a href="<?= $page_url ?>" class="dropdown-link">
                                    <?= $icon ?>
                                    <span><?= htmlspecialchars($page['display_name']) ?></span>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- 6. تنظیمات مدیریتی -->
            <?php if (!empty($admin_menu_pages)): ?>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle">
                        <i class="fas fa-cog"></i>
                        <span>تنظیمات مدیریتی</span>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <?php foreach ($admin_menu_pages as $page):
                            $page_url = "../pages/" . $page['file_path'];
                            $icon = $page['icon'] ? '<i class="' . $page['icon'] . '"></i>' : '';
                        ?>
                            <li>
                                <a href="<?= $page_url ?>" class="dropdown-link">
                                    <?= $icon ?>
                                    <span><?= htmlspecialchars($page['display_name']) ?></span>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php
            // 7. وظایف من
            renderMenuItem(findPageByName($all_pages, 'my_tasks'), $pdo);
            ?>
        </ul>
    </nav>

    <!-- User Info & Actions -->
    <div class="header-user">
        <div class="user-info dropdown" id="user-dropdown">
            <a href="#" class="user-profile-toggle dropdown-toggle">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name"><?= htmlspecialchars(current_user()['name'] ?? 'کاربر') ?></span>
                    <span class="user-role"><?= htmlspecialchars(current_user()['role'] ?? 'کاربر') ?></span>
                </div>
                <i class="fas fa-chevron-down user-dropdown-arrow"></i>
            </a>
            <ul class="dropdown-menu user-dropdown-menu">
                <?php if (has_permission('profile', 'view')): ?>
                    <li>
                        <a href="../pages/profile.php" class="dropdown-link">
                            <i class="fas fa-user-edit"></i>
                            <span>پروفایل کاربری</span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (has_permission('reminders', 'view')): ?>
                    <li>
                        <a href="../pages/reminders.php" class="dropdown-link">
                            <i class="fas fa-bell"></i>
                            <span>یادآوری‌ها</span>
                        </a>
                    </li>
                <?php endif; ?>
                <li class="dropdown-divider"></li>
                <li>
                    <a href="../pages/logout.php" class="dropdown-link logout-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>خروج از سیستم</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</header>

<script>
// Header Navigation JavaScript - Moved to end to ensure DOM elements exist
document.addEventListener('DOMContentLoaded', function() {
    console.log('Header JavaScript loaded');

    // Mobile menu toggle
    const mobileToggle = document.getElementById('mobile-menu-toggle');
    const mainNavigation = document.getElementById('main-navigation');

    if (mobileToggle && mainNavigation) {
        mobileToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            mainNavigation.classList.toggle('active');
        });
    }

    // Dropdown functionality
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    console.log('Found dropdown toggles:', dropdownToggles.length);

    dropdownToggles.forEach((toggle, index) => {
        console.log('Setting up dropdown', index, toggle);
        toggle.addEventListener('click', function(e) {
            console.log('Dropdown clicked:', index);
            e.preventDefault();
            const dropdown = this.closest('.dropdown');
            const isActive = dropdown.classList.contains('active');

            console.log('Dropdown current state:', isActive);

            // Close all other dropdowns
            document.querySelectorAll('.dropdown.active').forEach(d => {
                if (d !== dropdown) {
                    d.classList.remove('active');
                }
            });

            // Toggle current dropdown
            dropdown.classList.toggle('active', !isActive);
            console.log('Dropdown new state:', !isActive);
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });

    // Close mobile menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-link:not(.dropdown-toggle), .dropdown-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                mobileToggle?.classList.remove('active');
                mainNavigation?.classList.remove('active');
            }
        });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            mobileToggle?.classList.remove('active');
            mainNavigation?.classList.remove('active');
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });
});
</script>